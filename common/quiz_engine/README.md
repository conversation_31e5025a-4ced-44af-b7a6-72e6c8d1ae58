# Quiz Engine - Общий модуль для работы с тестами

Этот модуль предоставляет единую логику для отображения вопросов, обработки ответов и навигации по тестам во всех частях приложения.

## Основные компоненты

### 1. Модели данных (`models.py`)

- **QuizQuestion** - модель вопроса с вариантами ответов
- **QuizAnswer** - модель варианта ответа
- **QuizResult** - результат ответа на вопрос
- **QuizConfig** - конфигурация теста
- **QuizSession** - сессия прохождения теста
- **QuizType** - типы тестов (HOMEWORK, COURSE_ENTRY, MONTH_ENTRY, etc.)

### 2. Движок (`engine.py`)

- **QuizEngine** - основной класс для управления тестами
- `send_next_question()` - отправка следующего вопроса
- `handle_answer()` - обработка ответа пользователя
- Автоматическое управление таймерами и таймаутами

### 3. Утилиты (`utils.py`)

- `load_homework_questions()` - загрузка вопросов из БД
- `create_homework_config()` - создание конфигурации для ДЗ
- `calculate_topic_statistics()` - подсчет статистики по темам
- `format_quiz_results()` - форматирование результатов

### 4. Обработчики (`handlers.py`)

- `register_quiz_handlers()` - регистрация универсальных обработчиков

## Использование

### Базовый пример

```python
from common.quiz_engine import (
    quiz_engine, QuizSession, load_homework_questions, 
    create_homework_config, register_quiz_handlers
)

# 1. Регистрируем обработчики в роутере
register_quiz_handlers(router)

# 2. Загружаем вопросы
questions = await load_homework_questions(homework_id)

# 3. Создаем конфигурацию
config = create_homework_config(
    homework_name="Тест по химии",
    subject_name="Химия", 
    lesson_name="Алканы",
    questions=questions
)

# 4. Создаем сессию
session = QuizSession(
    user_id=user_id,
    chat_id=chat_id,
    config=config
)

# 5. Запускаем тест
await quiz_engine.send_next_question(session, state, bot, finish_callback)
```

### Обработка завершения теста

```python
async def finish_homework_quiz(session: QuizSession, state: FSMContext, bot):
    """Кастомная обработка завершения домашнего задания"""
    
    # Сохраняем результаты в БД
    await save_homework_results(session)
    
    # Вычисляем статистику
    statistics = calculate_topic_statistics(session, session.config.questions)
    
    # Отправляем результаты
    message = format_quiz_results(session, statistics)
    await bot.send_message(session.chat_id, message)
    
    # Удаляем сообщения теста
    await quiz_engine.cleanup_messages(session, bot)
```

### Колбэки для событий

```python
async def on_question_answered(session: QuizSession, result: QuizResult):
    """Обработка ответа на вопрос"""
    # Логирование, дополнительные проверки и т.д.
    pass

async def on_timeout(session: QuizSession, result: QuizResult):
    """Обработка таймаута"""
    # Специальная логика для пропущенных вопросов
    pass

# Добавляем колбэки в конфигурацию
config.on_question_answered = on_question_answered
config.on_timeout = on_timeout
```

## Преимущества

1. **Единая логика** - одинаковое поведение во всех типах тестов
2. **Переиспользование кода** - нет дублирования логики
3. **Легкая настройка** - гибкая конфигурация через QuizConfig
4. **Расширяемость** - легко добавлять новые типы тестов
5. **Надежность** - централизованная обработка ошибок и таймаутов

## Интеграция с существующим кодом

Модуль спроектирован для постепенной миграции:

1. Сначала можно использовать только для новых тестов
2. Затем постепенно мигрировать существующие обработчики
3. Сохраняется совместимость с текущими состояниями FSM

## Примеры интеграции

- `examples/homework_integration.py` - интеграция с домашними заданиями
- `examples/test_report_integration.py` - интеграция с тест-отчетами

Каждый пример показывает, как адаптировать существующий код для использования общего движка.
