"""
Общий модуль для работы с тестами и викторинами
"""

from .models import QuizQuestion, QuizAnswer, QuizResult, QuizConfig, QuizSession, QuizType, QuizStatistics
from .engine import QuizEngine, quiz_engine
from .handlers import QuizHandlers, register_quiz_handlers
from .utils import (
    load_homework_questions,
    load_bonus_test_questions,
    create_homework_config,
    create_course_entry_config,
    create_month_test_config,
    create_trial_ent_config,
    create_bonus_test_config,
    calculate_topic_statistics,
    format_quiz_results,
    generate_random_questions_for_subject
)

__all__ = [
    # Модели
    'QuizQuestion',
    'QuizAnswer',
    'QuizResult',
    'QuizConfig',
    'QuizSession',
    'QuizType',
    'QuizStatistics',

    # Движок
    'QuizEngine',
    'quiz_engine',

    # Обработчики
    'QuizHandlers',
    'register_quiz_handlers',

    # Утилиты
    'load_homework_questions',
    'load_bonus_test_questions',
    'create_homework_config',
    'create_course_entry_config',
    'create_month_test_config',
    'create_trial_ent_config',
    'create_bonus_test_config',
    'calculate_topic_statistics',
    'format_quiz_results',
    'generate_random_questions_for_subject'
]
