"""
Движок для отображения вопросов и навигации по тестам
"""

import asyncio
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, Optional, Callable

from aiogram import Bo<PERSON>
from aiogram.types import PollAnswer
from aiogram.fsm.context import FSMContext

from .models import QuizSession, QuizQuestion, QuizResult


class QuizEngine:
    """Движок для отображения вопросов и навигации по тесту"""
    
    def __init__(self):
        self.active_questions: Dict[str, Dict] = {}
        self.logger = logging.getLogger(__name__)
    
    async def send_next_question(self, session: QuizSession, state: FSMContext, bot: Bot, 
                               on_finish_callback: Optional[Callable] = None):
        """Отправить следующий вопрос или завершить тест"""
        if session.is_finished:
            if on_finish_callback:
                await on_finish_callback(session, state, bot)
            return
        
        question = session.current_question
        if not question:
            if on_finish_callback:
                await on_finish_callback(session, state, bot)
            return
        
        try:
            # Генерируем уникальный UUID для вопроса
            question_uuid = str(uuid.uuid4())
            session.current_question_uuid = question_uuid
            
            # Сохраняем информацию об активном вопросе
            self.active_questions[question_uuid] = {
                "session": session,
                "state": state,
                "bot": bot,
                "answered": False,
                "start_time": datetime.now(),
                "on_finish_callback": on_finish_callback
            }
            
            # Формируем варианты ответов для опроса
            options = [answer.text for answer in question.answers]
            correct_option_id = None
            
            # Находим правильный ответ
            for i, answer in enumerate(question.answers):
                if answer.is_correct:
                    correct_option_id = i
                    break
            
            if correct_option_id is None:
                correct_option_id = 0  # Fallback
            
            # Сохраняем варианты ответов в состоянии для обработки
            await state.update_data(
                current_question_id=question.id,
                current_question_uuid=question_uuid,
                current_answer_options=[{
                    'id': answer.id,
                    'text': answer.text,
                    'is_correct': answer.is_correct
                } for answer in question.answers],
                question_start_time=datetime.now().isoformat()
            )
            
            # Отправляем фото если есть
            photo_message = None
            if question.photo_path:
                photo_message = await bot.send_photo(
                    chat_id=session.chat_id,
                    photo=question.photo_path
                )
                session.messages_to_delete.append(photo_message.message_id)
            
            # Формируем текст вопроса с прогрессом
            question_text = question.text
            if session.config.show_progress:
                question_text = f"Вопрос {session.current_question_index + 1}/{session.total_questions}\n\n{question_text}"
            
            if session.config.show_subject_info and question.subject_name:
                question_text = f"📚 {question.subject_name}\n\n{question_text}"
            
            # Устанавливаем время закрытия опроса
            close_date = int((datetime.now() + timedelta(seconds=question.time_limit)).timestamp())
            
            # Отправляем опрос
            poll_message = await bot.send_poll(
                chat_id=session.chat_id,
                question=question_text,
                options=options,
                type="quiz",
                correct_option_id=correct_option_id,
                is_anonymous=False,
                close_date=close_date
            )
            
            session.current_poll_message_id = poll_message.message_id
            session.messages_to_delete.append(poll_message.message_id)
            
            self.logger.info(f"📝 Отправлен вопрос {session.current_question_index + 1}/{session.total_questions}")
            
            # Запускаем таймер для обработки таймаута
            asyncio.create_task(self._handle_question_timeout(question_uuid, question.time_limit))
            
        except Exception as e:
            self.logger.error(f"❌ Ошибка при отправке вопроса: {e}")
            await bot.send_message(session.chat_id, "❌ Ошибка при отправке вопроса")
    
    async def handle_answer(self, poll_answer: PollAnswer, state: FSMContext) -> bool:
        """Обработать ответ на вопрос"""
        try:
            # Получаем данные из состояния
            data = await state.get_data()
            question_uuid = data.get("current_question_uuid")
            current_answer_options = data.get("current_answer_options", [])
            question_start_time_str = data.get("question_start_time")
            
            if not question_uuid or question_uuid not in self.active_questions:
                return False
            
            question_info = self.active_questions[question_uuid]
            session = question_info["session"]
            
            # Отмечаем вопрос как отвеченный
            question_info["answered"] = True
            
            # Получаем выбранный ответ
            selected_option_index = poll_answer.option_ids[0]
            selected_answer = None
            
            if selected_option_index < len(current_answer_options):
                selected_answer = current_answer_options[selected_option_index]
            
            # Проверяем правильность ответа
            is_correct = selected_answer and selected_answer['is_correct']
            
            # Вычисляем время ответа
            time_spent = None
            if question_start_time_str:
                question_start_time = datetime.fromisoformat(question_start_time_str)
                time_spent = int((datetime.now() - question_start_time).total_seconds())
            
            # Создаем результат
            current_question = session.current_question
            result = QuizResult(
                question_id=current_question.id,
                selected_answer_id=selected_answer['id'] if selected_answer else None,
                is_correct=is_correct,
                time_spent=time_spent,
                microtopic_number=current_question.microtopic_number
            )
            
            # Добавляем результат в сессию
            session.add_result(result)
            
            # Вызываем колбэк если есть
            if session.config.on_question_answered:
                await session.config.on_question_answered(session, result)
            
            self.logger.info(f"📊 Ответ обработан: {'✅ правильно' if is_correct else '❌ неправильно'}")
            
            # Переходим к следующему вопросу
            session.next_question()
            
            # Отправляем следующий вопрос
            on_finish_callback = question_info.get("on_finish_callback")
            await self.send_next_question(session, state, question_info["bot"], on_finish_callback)
            
            # Очищаем активный вопрос
            del self.active_questions[question_uuid]
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Ошибка при обработке ответа: {e}")
            return False
    
    async def _handle_question_timeout(self, question_uuid: str, timeout_seconds: int):
        """Обработать таймаут вопроса"""
        try:
            await asyncio.sleep(timeout_seconds)
            
            if question_uuid not in self.active_questions:
                return
            
            question_info = self.active_questions[question_uuid]
            
            if question_info["answered"]:
                del self.active_questions[question_uuid]
                return
            
            session = question_info["session"]
            state = question_info["state"]
            bot = question_info["bot"]
            
            self.logger.info(f"⏰ Таймаут вопроса {question_uuid}")
            
            # Создаем результат для пропущенного вопроса
            current_question = session.current_question
            result = QuizResult(
                question_id=current_question.id,
                selected_answer_id=None,
                is_correct=False,
                time_spent=None,
                microtopic_number=current_question.microtopic_number
            )
            
            session.add_result(result)
            
            # Вызываем колбэк таймаута если есть
            if session.config.on_timeout:
                await session.config.on_timeout(session, result)
            
            # Переходим к следующему вопросу
            session.next_question()
            
            # Отправляем следующий вопрос
            on_finish_callback = question_info.get("on_finish_callback")
            await self.send_next_question(session, state, bot, on_finish_callback)
            
            # Очищаем активный вопрос
            del self.active_questions[question_uuid]
            
        except Exception as e:
            self.logger.error(f"❌ Ошибка при обработке таймаута: {e}")
    
    async def cleanup_messages(self, session: QuizSession, bot: Bot):
        """Удалить сообщения теста"""
        try:
            for message_id in session.messages_to_delete:
                try:
                    await bot.delete_message(session.chat_id, message_id)
                except Exception:
                    pass  # Игнорируем ошибки удаления
        except Exception as e:
            self.logger.error(f"❌ Ошибка при удалении сообщений: {e}")


# Глобальный экземпляр движка
quiz_engine = QuizEngine()
