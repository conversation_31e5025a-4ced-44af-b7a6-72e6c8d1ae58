"""
Пример интеграции общего модуля тестов с домашними заданиями
"""

from aiogram import Router, F
from aiogram.types import CallbackQuery, Message
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State

from common.quiz_engine import (
    quiz_engine, QuizSession, load_homework_questions, 
    create_homework_config, calculate_topic_statistics, format_quiz_results
)
from database import HomeworkRepository, StudentRepository, HomeworkResultRepository, QuestionResultRepository


class HomeworkStates(StatesGroup):
    """Состояния для домашних заданий"""
    confirmation = State()
    test_in_progress = State()
    repeat_test = State()


router = Router()


@router.callback_query(HomeworkStates.confirmation, F.data == "start_quiz")
async def start_homework_quiz(callback: CallbackQuery, state: FSMContext):
    """Начать домашнее задание с использованием общего движка"""
    data = await state.get_data()
    homework_id = data.get("homework_id")
    
    if not homework_id:
        await callback.answer("❌ Ошибка: данные домашнего задания не найдены", show_alert=True)
        return
    
    try:
        # Получаем информацию о домашнем задании
        homework = await HomeworkRepository.get_by_id(homework_id)
        if not homework:
            await callback.answer("❌ Домашнее задание не найдено", show_alert=True)
            return
        
        # Загружаем вопросы
        questions = await load_homework_questions(homework_id)
        if not questions:
            await callback.answer("❌ В этом домашнем задании нет вопросов", show_alert=True)
            return
        
        # Создаем конфигурацию теста
        config = create_homework_config(
            homework_name=homework.name,
            subject_name=homework.subject.name,
            lesson_name=homework.lesson.name,
            questions=questions
        )
        
        # Добавляем колбэки для обработки событий
        config.on_question_answered = on_homework_question_answered
        
        # Создаем сессию
        session = QuizSession(
            user_id=callback.from_user.id,
            chat_id=callback.message.chat.id,
            config=config
        )
        
        # Сохраняем дополнительные данные для домашнего задания
        session.config.metadata = {
            'homework_id': homework_id,
            'homework': {
                'id': homework.id,
                'name': homework.name,
                'subject_name': homework.subject.name,
                'lesson_name': homework.lesson.name
            }
        }
        
        # Устанавливаем состояние
        await state.set_state(HomeworkStates.test_in_progress)
        
        # Запускаем тест
        await quiz_engine.send_next_question(session, state, callback.bot, finish_homework_quiz)
        await callback.answer()
        
    except Exception as e:
        await callback.answer(f"❌ Ошибка при запуске теста: {str(e)}", show_alert=True)


async def on_homework_question_answered(session: QuizSession, result):
    """Колбэк для обработки ответа на вопрос домашнего задания"""
    # Здесь можно добавить специфичную логику для домашних заданий
    # Например, логирование или дополнительные проверки
    pass


async def finish_homework_quiz(session: QuizSession, state: FSMContext, bot):
    """Завершение домашнего задания"""
    try:
        # Получаем данные студента
        student = await StudentRepository.get_by_telegram_id(session.user_id)
        if not student:
            await bot.send_message(session.chat_id, "❌ Студент не найден в базе данных")
            return
        
        homework_data = session.config.metadata['homework']
        homework_id = session.config.metadata['homework_id']
        
        # Проверяем, проходил ли студент это ДЗ ранее
        existing_results = await HomeworkResultRepository.get_by_student_and_homework(
            student.id, homework_id
        )
        is_first_attempt = len(existing_results) == 0
        
        # Вычисляем баллы
        percentage = session.percentage
        points_earned = 0
        points_awarded = False
        
        if is_first_attempt and percentage == 100 and session.config.award_points:
            points_earned = len(session.results) * session.config.points_per_question
            points_awarded = True
        
        # Сохраняем результат в базу данных
        homework_result = await HomeworkResultRepository.create(
            student_id=student.id,
            homework_id=homework_id,
            total_questions=session.total_questions,
            correct_answers=session.score,
            points_earned=points_earned,
            is_first_attempt=is_first_attempt,
            points_awarded=points_awarded
        )
        
        # Сохраняем результаты по каждому вопросу
        for result_data in session.results:
            await QuestionResultRepository.create(
                homework_result_id=homework_result.id,
                question_id=result_data.question_id,
                selected_answer_id=result_data.selected_answer_id,
                is_correct=result_data.is_correct,
                time_spent=result_data.time_spent,
                microtopic_number=result_data.microtopic_number
            )
        
        # Обновляем баллы студента если нужно
        if points_awarded:
            await StudentRepository.update_points_and_level(student.id)
        
        # Вычисляем статистику по темам
        statistics = calculate_topic_statistics(session, session.config.questions)
        
        # Формируем сообщение с результатами
        if percentage == 100:
            result_emoji = "🎉"
            result_text = "Отличный результат!"
            if points_awarded:
                result_text += f"\n💰 Получено баллов: {points_earned}"
        elif percentage >= 80:
            result_emoji = "✅"
            result_text = "Хороший результат!"
        elif percentage >= 60:
            result_emoji = "⚠️"
            result_text = "Неплохо, но можно лучше"
        else:
            result_emoji = "❌"
            result_text = "Стоит повторить материал"
        
        if not is_first_attempt:
            result_text += "\n🔄 Повторное прохождение (баллы не начисляются)"
        
        message = (
            f"{result_emoji} Тест завершен!\n\n"
            f"📝 {homework_data['name']}\n"
            f"📚 {homework_data['subject_name']}\n"
            f"📊 Результат: {session.score}/{session.total_questions} ({percentage:.1f}%)\n"
            f"{result_text}\n\n"
            f"💡 Ты можешь пройти тест повторно для закрепления материала"
        )
        
        # Добавляем статистику по микротемам
        if statistics:
            message += "\n\n📈 Результаты по микротемам:\n"
            for stat in statistics:
                message += f"• {stat.topic_name} — {stat.percentage:.0f}% {stat.status_emoji}\n"
        
        # Кнопки для дальнейших действий
        from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [InlineKeyboardButton(text="🔄 Пройти еще раз", callback_data=f"homework_{homework_id}")],
            [InlineKeyboardButton(text="🏠 Главное меню", callback_data="back_to_main")]
        ])
        
        # Сохраняем данные для повторного прохождения
        await state.update_data(
            last_homework_id=homework_id,
            last_course_id=session.config.metadata.get("course_id"),
            last_subject_id=session.config.metadata.get("subject_id"),
            last_lesson_id=session.config.metadata.get("lesson_id")
        )
        
        await state.set_state(HomeworkStates.repeat_test)
        await bot.send_message(session.chat_id, message, reply_markup=keyboard)
        
        # Удаляем сообщения теста
        await quiz_engine.cleanup_messages(session, bot)
        
    except Exception as e:
        await bot.send_message(session.chat_id, f"❌ Ошибка при сохранении результатов: {str(e)}")


# Регистрируем обработчики
from common.quiz_engine import register_quiz_handlers
register_quiz_handlers(router)
