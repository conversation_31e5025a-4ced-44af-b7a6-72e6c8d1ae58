"""
Пример интеграции общего модуля тестов с тест-отчетами
"""

from aiogram import Router, F
from aiogram.types import CallbackQuery
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State

from common.quiz_engine import (
    quiz_engine, QuizSession, QuizType, generate_random_questions_for_subject,
    create_course_entry_config, create_month_test_config, 
    calculate_topic_statistics, format_quiz_results
)


class TestReportStates(StatesGroup):
    """Состояния для тест-отчетов"""
    main = State()
    test_in_progress = State()
    test_result = State()


router = Router()


@router.callback_query(F.data.startswith("course_entry_"))
async def start_course_entry_test(callback: CallbackQuery, state: FSMContext):
    """Начать входной тест курса"""
    subject_id = callback.data.replace("course_entry_", "")
    
    try:
        # Генерируем 30 случайных вопросов по предмету
        questions = await generate_random_questions_for_subject(
            subject_id=int(subject_id), 
            count=30
        )
        
        if not questions:
            await callback.answer("❌ Нет доступных вопросов по этому предмету", show_alert=True)
            return
        
        # Определяем название предмета
        subject_names = {
            "1": "История Казахстана",
            "2": "Математическая грамотность", 
            "3": "Математика",
            "4": "География",
            "5": "Биология",
            "6": "Химия",
            "7": "Информатика",
            "8": "Всемирная история"
        }
        subject_name = subject_names.get(subject_id, "Неизвестный предмет")
        
        # Создаем конфигурацию теста
        config = create_course_entry_config(subject_name, questions)
        
        # Создаем сессию
        session = QuizSession(
            user_id=callback.from_user.id,
            chat_id=callback.message.chat.id,
            config=config
        )
        
        # Сохраняем метаданные
        session.config.metadata = {
            'test_type': 'course_entry',
            'subject_id': subject_id,
            'subject_name': subject_name
        }
        
        await state.set_state(TestReportStates.test_in_progress)
        
        # Запускаем тест
        await quiz_engine.send_next_question(session, state, callback.bot, finish_test_report)
        await callback.answer()
        
    except Exception as e:
        await callback.answer(f"❌ Ошибка при запуске теста: {str(e)}", show_alert=True)


@router.callback_query(F.data.startswith("month_entry_"))
async def start_month_entry_test(callback: CallbackQuery, state: FSMContext):
    """Начать входной тест месяца"""
    parts = callback.data.replace("month_entry_", "").split("_")
    subject_id = parts[0]
    month_name = parts[1] if len(parts) > 1 else "Январь"
    
    try:
        # Генерируем 20 случайных вопросов по предмету
        questions = await generate_random_questions_for_subject(
            subject_id=int(subject_id), 
            count=20
        )
        
        if not questions:
            await callback.answer("❌ Нет доступных вопросов по этому предмету", show_alert=True)
            return
        
        subject_names = {
            "1": "История Казахстана",
            "2": "Математическая грамотность", 
            "3": "Математика",
            "4": "География",
            "5": "Биология",
            "6": "Химия",
            "7": "Информатика",
            "8": "Всемирная история"
        }
        subject_name = subject_names.get(subject_id, "Неизвестный предмет")
        
        # Создаем конфигурацию теста
        config = create_month_test_config(QuizType.MONTH_ENTRY, subject_name, month_name, questions)
        
        # Создаем сессию
        session = QuizSession(
            user_id=callback.from_user.id,
            chat_id=callback.message.chat.id,
            config=config
        )
        
        # Сохраняем метаданные
        session.config.metadata = {
            'test_type': 'month_entry',
            'subject_id': subject_id,
            'subject_name': subject_name,
            'month_name': month_name
        }
        
        await state.set_state(TestReportStates.test_in_progress)
        
        # Запускаем тест
        await quiz_engine.send_next_question(session, state, callback.bot, finish_test_report)
        await callback.answer()
        
    except Exception as e:
        await callback.answer(f"❌ Ошибка при запуске теста: {str(e)}", show_alert=True)


@router.callback_query(F.data.startswith("month_control_"))
async def start_month_control_test(callback: CallbackQuery, state: FSMContext):
    """Начать контрольный тест месяца"""
    parts = callback.data.replace("month_control_", "").split("_")
    subject_id = parts[0]
    month_name = parts[1] if len(parts) > 1 else "Январь"
    
    try:
        # Генерируем 20 случайных вопросов по предмету
        questions = await generate_random_questions_for_subject(
            subject_id=int(subject_id), 
            count=20
        )
        
        if not questions:
            await callback.answer("❌ Нет доступных вопросов по этому предмету", show_alert=True)
            return
        
        subject_names = {
            "1": "История Казахстана",
            "2": "Математическая грамотность", 
            "3": "Математика",
            "4": "География",
            "5": "Биология",
            "6": "Химия",
            "7": "Информатика",
            "8": "Всемирная история"
        }
        subject_name = subject_names.get(subject_id, "Неизвестный предмет")
        
        # Создаем конфигурацию теста
        config = create_month_test_config(QuizType.MONTH_CONTROL, subject_name, month_name, questions)
        
        # Создаем сессию
        session = QuizSession(
            user_id=callback.from_user.id,
            chat_id=callback.message.chat.id,
            config=config
        )
        
        # Сохраняем метаданные
        session.config.metadata = {
            'test_type': 'month_control',
            'subject_id': subject_id,
            'subject_name': subject_name,
            'month_name': month_name
        }
        
        await state.set_state(TestReportStates.test_in_progress)
        
        # Запускаем тест
        await quiz_engine.send_next_question(session, state, callback.bot, finish_test_report)
        await callback.answer()
        
    except Exception as e:
        await callback.answer(f"❌ Ошибка при запуске теста: {str(e)}", show_alert=True)


async def finish_test_report(session: QuizSession, state: FSMContext, bot):
    """Завершение тест-отчета"""
    try:
        metadata = session.config.metadata
        test_type = metadata['test_type']
        subject_name = metadata['subject_name']
        
        # Вычисляем статистику по темам
        statistics = calculate_topic_statistics(session, session.config.questions)
        
        # Формируем сообщение в зависимости от типа теста
        if test_type == "course_entry":
            title = "📊 Входной тест курса пройден"
        elif test_type == "month_entry":
            month_name = metadata.get('month_name', '')
            title = f"📊 Входной тест месяца {month_name} пройден"
        else:  # month_control
            month_name = metadata.get('month_name', '')
            title = f"📊 Контрольный тест месяца {month_name} пройден"
        
        percentage = session.percentage
        message = (
            f"{title}\n"
            f"Результат:\n"
            f"📗 {subject_name}:\n"
            f"Верных: {session.score} / {session.total_questions}\n"
        )
        
        # Добавляем статистику по темам
        if statistics:
            for stat in statistics:
                message += f"• {stat.topic_name} — {stat.percentage:.0f}% {stat.status_emoji}\n"
        
        # Добавляем анализ сильных и слабых тем
        from common.statistics import add_strong_and_weak_topics
        topics_percentages = {stat.topic_name: stat.percentage for stat in statistics}
        message = add_strong_and_weak_topics(message, topics_percentages)
        
        # Кнопка возврата
        from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [InlineKeyboardButton(text="🔙 Назад к тестам", callback_data="student_tests")],
            [InlineKeyboardButton(text="🏠 Главное меню", callback_data="back_to_main")]
        ])
        
        await state.set_state(TestReportStates.test_result)
        await bot.send_message(session.chat_id, message, reply_markup=keyboard)
        
        # Удаляем сообщения теста
        await quiz_engine.cleanup_messages(session, bot)
        
    except Exception as e:
        await bot.send_message(session.chat_id, f"❌ Ошибка при завершении теста: {str(e)}")


# Регистрируем обработчики
from common.quiz_engine import register_quiz_handlers
register_quiz_handlers(router)
