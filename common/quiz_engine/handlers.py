"""
Обработчики для интеграции с aiogram
"""

from aiogram import Router
from aiogram.types import PollAnswer
from aiogram.fsm.context import FSMContext

from .engine import quiz_engine


class QuizHandlers:
    """Класс для регистрации обработчиков тестов"""
    
    def __init__(self, router: Router):
        self.router = router
        self.register_handlers()
    
    def register_handlers(self):
        """Регистрация общих обработчиков"""
        
        @self.router.poll_answer()
        async def handle_quiz_poll_answer(poll_answer: PollAnswer, state: FSMContext):
            """Универсальный обработчик ответов на опросы"""
            # Проверяем, есть ли активная сессия теста
            data = await state.get_data()
            question_uuid = data.get("current_question_uuid")
            
            if question_uuid and question_uuid in quiz_engine.active_questions:
                await quiz_engine.handle_answer(poll_answer, state)


def register_quiz_handlers(router: Router) -> QuizHandlers:
    """Зарегистрировать обработчики тестов"""
    return QuizHandlers(router)
