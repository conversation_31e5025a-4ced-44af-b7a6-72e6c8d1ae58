"""
Модели данных для системы тестирования
"""

from dataclasses import dataclass
from typing import List, Dict, Optional, Any, Callable
from datetime import datetime
from enum import Enum


class QuizType(Enum):
    """Типы тестов"""
    HOMEWORK = "homework"
    COURSE_ENTRY = "course_entry"
    MONTH_ENTRY = "month_entry"
    MONTH_CONTROL = "month_control"
    TRIAL_ENT = "trial_ent"
    BONUS_TEST = "bonus_test"


@dataclass
class QuizAnswer:
    """Вариант ответа на вопрос"""
    id: Optional[int]
    text: str
    is_correct: bool
    order_number: int = 1


@dataclass
class QuizQuestion:
    """Вопрос теста"""
    id: int
    text: str
    answers: List[QuizAnswer]
    photo_path: Optional[str] = None
    time_limit: int = 30
    order_number: int = 1
    microtopic_number: Optional[int] = None
    subject_name: Optional[str] = None
    topic_name: Optional[str] = None
    
    def get_correct_answer(self) -> Optional[QuizAnswer]:
        """Получить правильный ответ"""
        for answer in self.answers:
            if answer.is_correct:
                return answer
        return None


@dataclass
class QuizResult:
    """Результат ответа на вопрос"""
    question_id: int
    selected_answer_id: Optional[int]
    is_correct: bool
    time_spent: Optional[int]
    microtopic_number: Optional[int] = None


@dataclass
class QuizConfig:
    """Конфигурация теста"""
    quiz_type: QuizType
    title: str
    description: str
    questions: List[QuizQuestion]
    
    # Настройки отображения
    show_progress: bool = True
    show_timer: bool = True
    show_subject_info: bool = True
    
    # Настройки результатов
    save_to_database: bool = True
    award_points: bool = False
    points_per_question: int = 3
    require_100_percent: bool = False
    
    # Колбэки для обработки событий
    on_question_answered: Optional[Callable] = None
    on_quiz_completed: Optional[Callable] = None
    on_timeout: Optional[Callable] = None
    
    # Дополнительные данные
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class QuizSession:
    """Сессия прохождения теста"""
    user_id: int
    chat_id: int
    config: QuizConfig
    current_question_index: int = 0
    score: int = 0
    results: List[QuizResult] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    is_completed: bool = False
    
    # Технические данные
    current_question_uuid: Optional[str] = None
    current_poll_message_id: Optional[int] = None
    messages_to_delete: List[int] = None
    
    def __post_init__(self):
        if self.results is None:
            self.results = []
        if self.messages_to_delete is None:
            self.messages_to_delete = []
        if self.start_time is None:
            self.start_time = datetime.now()
    
    @property
    def current_question(self) -> Optional[QuizQuestion]:
        """Получить текущий вопрос"""
        if 0 <= self.current_question_index < len(self.config.questions):
            return self.config.questions[self.current_question_index]
        return None
    
    @property
    def total_questions(self) -> int:
        """Общее количество вопросов"""
        return len(self.config.questions)
    
    @property
    def is_finished(self) -> bool:
        """Проверить, завершен ли тест"""
        return self.current_question_index >= self.total_questions or self.is_completed
    
    @property
    def percentage(self) -> float:
        """Процент правильных ответов"""
        if not self.results:
            return 0.0
        correct_count = sum(1 for result in self.results if result.is_correct)
        return (correct_count / len(self.results)) * 100 if self.results else 0.0
    
    def add_result(self, result: QuizResult):
        """Добавить результат ответа"""
        self.results.append(result)
        if result.is_correct:
            self.score += 1
    
    def next_question(self):
        """Перейти к следующему вопросу"""
        self.current_question_index += 1
    
    def complete(self):
        """Завершить тест"""
        self.is_completed = True
        self.end_time = datetime.now()


@dataclass
class QuizStatistics:
    """Статистика по темам/микротемам"""
    topic_name: str
    correct_answers: int = 0
    total_answers: int = 0
    
    @property
    def percentage(self) -> float:
        """Процент правильных ответов по теме"""
        return (self.correct_answers / self.total_answers * 100) if self.total_answers > 0 else 0.0
    
    @property
    def status_emoji(self) -> str:
        """Эмодзи статуса по проценту"""
        if self.percentage >= 80:
            return "✅"
        elif self.percentage <= 40:
            return "❌"
        else:
            return "⚠️"
