"""
Утилиты для работы с тестами
"""

from typing import List, Dict
from database import QuestionRepository, AnswerOptionRepository
from .models import QuizQuestion, QuizAnswer, QuizConfig, QuizType, QuizStatistics


async def load_homework_questions(homework_id: int) -> List[QuizQuestion]:
    """Загрузить вопросы домашнего задания из базы данных"""
    questions = await QuestionRepository.get_by_homework(homework_id)
    quiz_questions = []
    
    for question in questions:
        # Получаем варианты ответов
        answer_options = await AnswerOptionRepository.get_by_question(question.id)
        
        quiz_answers = []
        for option in answer_options:
            quiz_answers.append(QuizAnswer(
                id=option.id,
                text=option.text,
                is_correct=option.is_correct,
                order_number=option.order_number
            ))
        
        # Сортируем ответы по порядку
        quiz_answers.sort(key=lambda x: x.order_number)
        
        quiz_questions.append(QuizQuestion(
            id=question.id,
            text=question.text,
            answers=quiz_answers,
            photo_path=question.photo_path,
            time_limit=question.time_limit,
            order_number=question.order_number,
            microtopic_number=question.microtopic_number,
            subject_name=question.subject.name if question.subject else None
        ))
    
    # Сортируем вопросы по порядку
    quiz_questions.sort(key=lambda x: x.order_number)
    return quiz_questions


async def load_bonus_test_questions(bonus_test_id: int) -> List[QuizQuestion]:
    """Загрузить вопросы бонусного теста из базы данных"""
    from database import BonusQuestionRepository, BonusAnswerOptionRepository
    
    bonus_question_repo = BonusQuestionRepository()
    questions = await bonus_question_repo.get_by_bonus_test(bonus_test_id)
    quiz_questions = []
    
    for question in questions:
        # Получаем варианты ответов
        answer_options = await BonusAnswerOptionRepository.get_by_question(question.id)
        
        quiz_answers = []
        for option in answer_options:
            quiz_answers.append(QuizAnswer(
                id=option.id,
                text=option.text,
                is_correct=option.is_correct,
                order_number=option.order_number
            ))
        
        # Сортируем ответы по порядку
        quiz_answers.sort(key=lambda x: x.order_number)
        
        quiz_questions.append(QuizQuestion(
            id=question.id,
            text=question.text,
            answers=quiz_answers,
            photo_path=question.photo_path,
            time_limit=question.time_limit,
            order_number=question.order_number
        ))
    
    # Сортируем вопросы по порядку
    quiz_questions.sort(key=lambda x: x.order_number)
    return quiz_questions


def create_homework_config(homework_name: str, subject_name: str, lesson_name: str, 
                          questions: List[QuizQuestion]) -> QuizConfig:
    """Создать конфигурацию для домашнего задания"""
    return QuizConfig(
        quiz_type=QuizType.HOMEWORK,
        title=homework_name,
        description=f"📚 {subject_name} - {lesson_name}",
        questions=questions,
        show_progress=True,
        show_timer=True,
        show_subject_info=True,
        save_to_database=True,
        award_points=True,
        points_per_question=3,
        require_100_percent=True
    )


def create_course_entry_config(subject_name: str, questions: List[QuizQuestion]) -> QuizConfig:
    """Создать конфигурацию для входного теста курса"""
    return QuizConfig(
        quiz_type=QuizType.COURSE_ENTRY,
        title="Входной тест курса",
        description=f"📚 {subject_name}",
        questions=questions,
        show_progress=True,
        show_timer=True,
        show_subject_info=True,
        save_to_database=False,
        award_points=False
    )


def create_month_test_config(test_type: QuizType, subject_name: str, month_name: str, 
                           questions: List[QuizQuestion]) -> QuizConfig:
    """Создать конфигурацию для теста месяца"""
    if test_type == QuizType.MONTH_ENTRY:
        title = f"Входной тест месяца {month_name}"
    else:
        title = f"Контрольный тест месяца {month_name}"
    
    return QuizConfig(
        quiz_type=test_type,
        title=title,
        description=f"📚 {subject_name}",
        questions=questions,
        show_progress=True,
        show_timer=True,
        show_subject_info=True,
        save_to_database=False,
        award_points=False
    )


def create_trial_ent_config(questions: List[QuizQuestion]) -> QuizConfig:
    """Создать конфигурацию для пробного ЕНТ"""
    return QuizConfig(
        quiz_type=QuizType.TRIAL_ENT,
        title="Пробный ЕНТ",
        description="Симуляция ЕНТ: 130 вопросов",
        questions=questions,
        show_progress=True,
        show_timer=False,
        show_subject_info=True,
        save_to_database=False,
        award_points=False
    )


def create_bonus_test_config(test_name: str, questions: List[QuizQuestion]) -> QuizConfig:
    """Создать конфигурацию для бонусного теста"""
    return QuizConfig(
        quiz_type=QuizType.BONUS_TEST,
        title=test_name,
        description="🧪 Бонусный тест",
        questions=questions,
        show_progress=True,
        show_timer=True,
        show_subject_info=False,
        save_to_database=True,
        award_points=False
    )


def calculate_topic_statistics(session, questions: List[QuizQuestion]) -> List[QuizStatistics]:
    """Вычислить статистику по темам/микротемам"""
    stats_dict = {}
    
    for i, result in enumerate(session.results):
        if i < len(questions):
            question = questions[i]
            topic_name = question.topic_name or f"Микротема {question.microtopic_number}" if question.microtopic_number else "Общие вопросы"
            
            if topic_name not in stats_dict:
                stats_dict[topic_name] = QuizStatistics(topic_name=topic_name)
            
            stats_dict[topic_name].total_answers += 1
            if result.is_correct:
                stats_dict[topic_name].correct_answers += 1
    
    return list(stats_dict.values())


def format_quiz_results(session, statistics: List[QuizStatistics]) -> str:
    """Форматировать результаты теста"""
    percentage = session.percentage
    
    if percentage == 100:
        result_emoji = "🎉"
        result_text = "Отличный результат!"
    elif percentage >= 80:
        result_emoji = "✅"
        result_text = "Хороший результат!"
    elif percentage >= 60:
        result_emoji = "⚠️"
        result_text = "Неплохо, но можно лучше"
    else:
        result_emoji = "❌"
        result_text = "Стоит повторить материал"
    
    message = (
        f"{result_emoji} Тест завершен!\n\n"
        f"📝 {session.config.title}\n"
        f"📊 Результат: {session.score}/{session.total_questions} ({percentage:.1f}%)\n"
        f"{result_text}\n\n"
    )
    
    # Добавляем статистику по темам если есть
    if statistics:
        message += "📈 Результаты по темам:\n"
        for stat in statistics:
            message += f"• {stat.topic_name} — {stat.percentage:.0f}% {stat.status_emoji}\n"
    
    return message


async def generate_random_questions_for_subject(subject_id: int, count: int, 
                                              microtopic_numbers: List[int] = None) -> List[QuizQuestion]:
    """Генерировать случайные вопросы по предмету для тестов"""
    # Получаем все домашние задания по предмету
    from database import HomeworkRepository
    
    homeworks = await HomeworkRepository.get_by_subject(subject_id)
    all_questions = []
    
    for homework in homeworks:
        homework_questions = await load_homework_questions(homework.id)
        
        # Фильтруем по микротемам если указаны
        if microtopic_numbers:
            homework_questions = [q for q in homework_questions 
                                if q.microtopic_number in microtopic_numbers]
        
        all_questions.extend(homework_questions)
    
    # Перемешиваем и берем нужное количество
    import random
    random.shuffle(all_questions)
    
    return all_questions[:count]
